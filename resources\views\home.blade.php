@extends('layouts.app')

@section('title', 'Guzexs Gaming Official - <PERSON><PERSON><PERSON>i Dunia Gaming Bersama Kami')
@section('description', 'Channel YouTube gaming terbaik dengan konten eksklusif dan produk digital berkualitas tinggi. Bergabunglah dengan komunitas gaming Guzexs!')

@section('content')
<!-- Hero Section with Parallax -->
<section class="hero-section" id="hero">
    <div class="hero-bg parallax-element" data-speed="0.3"></div>
    
    <!-- Animated Background Elements -->
    <div class="position-absolute w-100 h-100" style="top: 0; left: 0; overflow: hidden; z-index: 1;">
        <div class="position-absolute" style="top: 20%; left: 10%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(0,123,255,0.1) 0%, transparent 70%); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: 15%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,193,7,0.1) 0%, transparent 70%); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="top: 40%; left: 70%; width: 80px; height: 80px; background: radial-gradient(circle, rgba(0,123,255,0.15) 0%, transparent 70%); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>
    
    <div class="container hero-content">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="hero-title">
                    GUZEXS<br>
                    <span class="text-warning">GAMING</span>
                </h1>
                <p class="hero-subtitle">
                    Jelajahi Dunia Gaming Bersama Guzexs! 🎮
                </p>
                <p class="lead text-light mb-4">
                    Channel YouTube gaming terbaik dengan konten eksklusif, tutorial gaming, review game terbaru, dan produk digital berkualitas tinggi untuk para gamers sejati.
                </p>
                
                <div class="d-flex flex-wrap gap-3">
                    <a href="{{ route('youtube') }}" class="btn btn-gaming btn-lg">
                        <i class="fab fa-youtube me-2"></i>
                        Lihat Channel YouTube
                    </a>
                    <a href="{{ route('products.index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-download me-2"></i>
                        Jelajahi Produk Digital
                    </a>
                </div>
                
                <!-- Stats -->
                <div class="row mt-5">
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="gaming-font text-primary mb-0" data-count="50000">0</h3>
                            <small class="text-muted">Subscribers</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="gaming-font text-warning mb-0" data-count="1000">0</h3>
                            <small class="text-muted">Videos</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h3 class="gaming-font text-success mb-0" data-count="100">0</h3>
                            <small class="text-muted">Products</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6" data-aos="fade-left">
                <div class="position-relative">
                    <!-- Gaming Controller Animation -->
                    <div class="text-center">
                        <i class="fas fa-gamepad" style="font-size: 15rem; color: var(--primary-blue); opacity: 0.1; animation: pulse 2s infinite;"></i>
                    </div>
                    
                    <!-- Floating Elements -->
                    <div class="position-absolute top-0 start-0 w-100 h-100">
                        <div class="position-absolute" style="top: 20%; left: 20%; animation: float 3s ease-in-out infinite;">
                            <i class="fas fa-star text-warning" style="font-size: 2rem;"></i>
                        </div>
                        <div class="position-absolute" style="top: 30%; right: 20%; animation: float 4s ease-in-out infinite reverse;">
                            <i class="fas fa-trophy text-warning" style="font-size: 2.5rem;"></i>
                        </div>
                        <div class="position-absolute" style="bottom: 30%; left: 30%; animation: float 5s ease-in-out infinite;">
                            <i class="fas fa-rocket text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="position-absolute bottom-0 start-50 translate-middle-x mb-4" data-aos="fade-up" data-aos-delay="1000">
        <div class="text-center">
            <small class="text-muted d-block mb-2">Scroll untuk melihat lebih banyak</small>
            <i class="fas fa-chevron-down text-primary" style="animation: bounce 2s infinite;"></i>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="py-5" style="background: var(--gradient-dark);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h2 class="gaming-font text-primary mb-4">Tentang Guzexs Gaming</h2>
                <p class="lead text-light">
                    Guzexs Gaming adalah channel YouTube gaming yang berdedikasi untuk memberikan konten gaming terbaik, tutorial mendalam, dan review game terbaru untuk komunitas gaming Indonesia.
                </p>
                <p class="text-muted">
                    Kami tidak hanya membuat konten, tetapi juga menyediakan produk digital eksklusif seperti template video, preset audio, aset grafis, dan tools gaming yang akan membantu para content creator dan gamers meningkatkan kualitas konten mereka.
                </p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success me-3"></i>
                            <span>Konten Gaming Berkualitas</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success me-3"></i>
                            <span>Produk Digital Eksklusif</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success me-3"></i>
                            <span>Komunitas Gaming Aktif</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle text-success me-3"></i>
                            <span>Support 24/7</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6" data-aos="fade-left">
                <div class="gaming-card p-4 text-center">
                    <i class="fas fa-users text-primary mb-3" style="font-size: 4rem;"></i>
                    <h4 class="gaming-font text-warning">Join Our Community</h4>
                    <p class="text-muted">
                        Bergabunglah dengan ribuan gamers lainnya dan dapatkan akses eksklusif ke konten premium, early access produk baru, dan diskusi gaming yang seru!
                    </p>
                    <a href="#" class="btn btn-gaming">
                        <i class="fab fa-discord me-2"></i>
                        Join Discord
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Content Section -->
<section class="py-5" style="background: var(--dark-bg);">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="gaming-font text-primary">Konten Unggulan</h2>
            <p class="lead text-muted">Video terbaru dan terpopuler dari channel Guzexs Gaming</p>
        </div>
        
        <div class="row" id="featured-videos">
            <!-- Videos will be loaded here via JavaScript/API -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="gaming-card">
                    <div class="position-relative">
                        <img src="https://via.placeholder.com/400x225/0a0e27/007bff?text=Loading..." class="card-img-top" alt="Video Thumbnail">
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <i class="fas fa-play-circle text-primary" style="font-size: 3rem; opacity: 0.8;"></i>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title text-light">Loading Latest Videos...</h5>
                        <p class="card-text text-muted">Menampilkan video terbaru dari channel YouTube Guzexs Gaming</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4" data-aos="fade-up">
            <a href="{{ route('youtube') }}" class="btn btn-gaming btn-lg">
                <i class="fab fa-youtube me-2"></i>
                Lihat Semua Video
            </a>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-5" style="background: var(--gradient-dark);">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="gaming-font text-primary">Produk Digital Unggulan</h2>
            <p class="lead text-muted">Template, preset, dan aset digital eksklusif untuk content creator</p>
        </div>
        
        <div class="row">
            @for($i = 1; $i <= 3; $i++)
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $i * 100 }}">
                <div class="gaming-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://via.placeholder.com/400x250/0a0e27/007bff?text=Product+{{ $i }}" class="card-img-top" alt="Product {{ $i }}">
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-warning">Featured</span>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title text-light">Gaming Template Pack {{ $i }}</h5>
                        <p class="card-text text-muted flex-grow-1">
                            Template video gaming profesional dengan efek visual yang menakjubkan dan mudah dikustomisasi.
                        </p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span class="text-muted text-decoration-line-through">Rp 150.000</span>
                                <span class="text-warning fw-bold ms-2">Rp 99.000</span>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-gaming">
                                    <i class="fas fa-cart-plus me-1"></i>
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="d-flex align-items-center">
                                <div class="text-warning me-2">
                                    @for($j = 1; $j <= 5; $j++)
                                        <i class="fas fa-star{{ $j <= 4 ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                                <small class="text-muted">(24 reviews)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endfor
        </div>
        
        <div class="text-center mt-4" data-aos="fade-up">
            <a href="{{ route('products.index') }}" class="btn btn-gaming btn-lg">
                <i class="fas fa-store me-2"></i>
                Lihat Semua Produk
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5" style="background: var(--dark-bg);">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="gaming-font text-primary">Apa Kata Mereka</h2>
            <p class="lead text-muted">Testimoni dari komunitas gaming Guzexs</p>
        </div>
        
        <div class="row">
            @php
            $testimonials = [
                [
                    'name' => 'Ahmad Gaming',
                    'role' => 'Content Creator',
                    'avatar' => 'https://via.placeholder.com/80x80/007bff/ffffff?text=AG',
                    'text' => 'Template dari Guzexs Gaming sangat membantu channel YouTube saya! Kualitasnya professional dan mudah digunakan.',
                    'rating' => 5
                ],
                [
                    'name' => 'Sarah Streamer',
                    'role' => 'Twitch Streamer',
                    'avatar' => 'https://via.placeholder.com/80x80/ffc107/000000?text=SS',
                    'text' => 'Preset audio dari Guzexs membuat stream saya terdengar lebih professional. Highly recommended!',
                    'rating' => 5
                ],
                [
                    'name' => 'Budi Gamer',
                    'role' => 'Gaming Enthusiast',
                    'avatar' => 'https://via.placeholder.com/80x80/28a745/ffffff?text=BG',
                    'text' => 'Konten YouTube Guzexs selalu update dan informatif. Channel gaming terbaik di Indonesia!',
                    'rating' => 5
                ]
            ];
            @endphp
            
            @foreach($testimonials as $index => $testimonial)
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ ($index + 1) * 100 }}">
                <div class="gaming-card h-100 p-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="{{ $testimonial['avatar'] }}" alt="{{ $testimonial['name'] }}" class="rounded-circle me-3" width="60" height="60">
                        <div>
                            <h6 class="text-light mb-0">{{ $testimonial['name'] }}</h6>
                            <small class="text-muted">{{ $testimonial['role'] }}</small>
                        </div>
                    </div>
                    
                    <div class="text-warning mb-3">
                        @for($i = 1; $i <= $testimonial['rating']; $i++)
                            <i class="fas fa-star"></i>
                        @endfor
                    </div>
                    
                    <p class="text-muted mb-0">
                        "{{ $testimonial['text'] }}"
                    </p>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-5" style="background: var(--gradient-primary);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h2 class="gaming-font text-white mb-3">Stay Updated!</h2>
                <p class="text-white mb-0">
                    Dapatkan notifikasi untuk video terbaru, produk baru, dan penawaran eksklusif dari Guzexs Gaming.
                </p>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <form class="d-flex gap-3" id="newsletter-form">
                    <input type="email" class="form-control form-control-lg" placeholder="Masukkan email Anda" required>
                    <button type="submit" class="btn btn-dark btn-lg px-4">
                        Subscribe
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

@endsection

@push('styles')
<style>
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 0.1; }
        50% { opacity: 0.3; }
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    
    .gaming-card:hover .card-img-top {
        transform: scale(1.05);
        transition: transform 0.3s ease;
    }
    
    .card-img-top {
        transition: transform 0.3s ease;
    }
</style>
@endpush

@push('scripts')
<script>
    // Counter Animation
    function animateCounters() {
        const counters = document.querySelectorAll('[data-count]');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current).toLocaleString();
            }, 16);
        });
    }
    
    // Trigger counter animation when hero section is in view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    });
    
    observer.observe(document.getElementById('hero'));
    
    // Newsletter form
    document.getElementById('newsletter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = this.querySelector('input[type="email"]').value;
        
        // Here you would typically send the email to your backend
        alert('Terima kasih! Anda akan mendapatkan update terbaru dari Guzexs Gaming.');
        this.reset();
    });
    
    // Load YouTube videos (placeholder - you'll need to implement YouTube API)
    function loadFeaturedVideos() {
        // This is a placeholder. In real implementation, you would:
        // 1. Make API call to YouTube Data API
        // 2. Get latest videos from Guzexs Gaming channel
        // 3. Update the #featured-videos container
        
        console.log('Loading featured videos...');
    }
    
    // Load videos when page loads
    document.addEventListener('DOMContentLoaded', loadFeaturedVideos);
</script>
@endpush
